/**
* <AUTHOR> <PERSON><PERSON><PERSON>  
* @date :  4 Sept 2019, Last Modified: NA
* @description: FAUXCBUF-1968,Helper class to populate Address on the WorkOrder on Location Visitoraddress update.
*/
public without sharing class LocationHelper {
    /*Method to update WO address based on the Location*/
    public void updateRelatedWOAddress(List<Schema.Location> lstLocation){
        try{
            if(!lstLocation.isEmpty()){
                Map<Id,Id> mapLocationIdAddressId=new Map<Id,Id>();
                Map<Id,GenericAddressHelper.AddressWrapper> mapAddressIdAndAddress=new Map<Id,GenericAddressHelper.AddressWrapper>();
                Map<Id,GenericAddressHelper.AddressWrapper> mapLocationIdAddress=new Map<Id,GenericAddressHelper.AddressWrapper>();
                Map<Id,Id> mapAssetIdLocId=new Map<Id,Id>();
                Map<Id,GenericAddressHelper.AddressWrapper> mapAssetIdAddress=new Map<Id,GenericAddressHelper.AddressWrapper>();
                Map<WorkOrder,Id> mapWOAssetId=new Map<WorkOrder,Id>();
                Map<WorkOrder,GenericAddressHelper.AddressWrapper> mapWOAddress=new Map<WorkOrder,GenericAddressHelper.AddressWrapper>();
                
                mapLocationIdAddressId=GenericAddressHelper.getAddress(lstLocation);
                for(Schema.Address objAddress:[SELECT Id,Street,City,State,Country,PostalCode,countryCode 
                                               FROM address 
                                               WHERE Id IN:mapLocationIdAddressId.values() LIMIT 50000]){
                    mapAddressIdAndAddress.put(objAddress.Id,new GenericAddressHelper.addressWrapper(objAddress));
                }
                mapLocationIdAddress=GenericAddressHelper.buildLocationIdAddressMap(mapLocationIdAddressId,mapAddressIdAndAddress);
                
                mapAssetIdLocId=GenericAddressHelper.getAsset(mapLocationIdAddress.keyset());
                mapAssetIdAddress=GenericAddressHelper.buildAssetIdAddressMap(mapAssetIdLocId,mapLocationIdAddress);
                
                mapWOAssetId=GenericAddressHelper.getWorkOrder(mapAssetIdAddress.keyset());
                mapWOAddress=GenericAddressHelper.buildWOAssetIdMap(mapWOAssetId,mapAssetIdAddress);
                
                GenericAddressHelper.updateWorkOrder(mapWOAddress);
            }
        }catch(Exception e){
            throw e;
        }
    }
    /*Method to populate country code on Location Insert*/
    public void populateLocationCountryCode(List<Schema.Location> lstLocation){
        try{
            Map<Id,String> mapAddressIdCode=new Map<Id,String>();
            Map<Id,String> mapLocIdcode=new Map<Id,String>();
            List<Id> lstAddress=new List<Id>();
            Set<Id> setLocation=new Set<Id>();
            for(Schema.Location objLoc:lstLocation){
                lstAddress.add(objLoc.VisitorAddressId);
           }
            for(Schema.Address objAddress:[SELECT Id,CountryCode 
                                          FROM address 
                                          WHERE Id IN:lstAddress LIMIT 50000]){
                mapAddressIdCode.put(objAddress.Id,objAddress.CountryCode);
            }
            for(Schema.Location objLoc:lstLocation){
                if(mapAddressIdCode.containskey(objLoc.VisitorAddressId)){
                    objLoc.Address_Country_Code__c=mapAddressIdCode.get(objLoc.VisitorAddressId);
                    if(Trigger.isUpdate){
                    	mapLocIdcode.put(objLoc.Id,mapAddressIdCode.get(objLoc.VisitorAddressId));
                    }
                }else if(objLoc.VisitorAddressId==Null){
                    objLoc.Address_Country_Code__c=Constant.SPACE;
                    if(Trigger.isUpdate){
                    	mapLocIdcode.put(objLoc.Id,Constant.SPACE);
                    }
                }else{
                    continue;
                }
            } 
            if(!mapLocIdcode.isEmpty()){
                populateAssetCountryCode(mapLocIdcode);
            }
        }catch(Exception e){
            throw e;
        }
    }
    
    /*Method to update Asset Country Code*/
    public void populateAssetCountryCode(Map<Id,String> mapLocIdcode){
        List<Asset> lstAssetToUpdate=new List<Asset>();
        Map<Id,Id> mapAssetIdLocId=GenericAddressHelper.getAsset(mapLocIdcode.keyset());
        for(Asset objAsset:[SELECT Id,Location_address_country_code__c 
                         FROM asset 
                         WHERE Id IN:mapAssetIdLocId.keyset() LIMIT 50000]){
            if(mapLocIdcode.containskey(mapAssetIdLocId.get(objAsset.Id))){
                objAsset.Location_address_country_code__c=mapLocIdcode.get(mapAssetIdLocId.get(objAsset.Id));
                lstAssetToUpdate.add(objAsset);
            }
        }
        if(!lstAssetToUpdate.isEmpty()){
        	Database.update(lstAssetToUpdate);
        }
    }
    
    /*Method to clear WO Address when the Location has No VisitorAddress*/
    public void clearWOAddress(List<Schema.Location> lstLocation){
        if(!lstLocation.isEmpty()){
            List<WorkOrder> lstWOToBeUpdated=new List<WorkOrder>();
            WorkOrder objUpdateWO=new WorkOrder();
            for(WorkOrder objWO:[SELECT Id,Street,city,state,country,postalcode 
                                 FROM WorkOrder 
                                 WHERE asset.locationId IN:lstLocation LIMIT 50000]){
                                     objUpdateWO=new WorkOrder();
                                     objUpdateWO.Id=objWO.Id;
                                     objUpdateWO.street=Constant.SPACE;
                                     objUpdateWO.city=Constant.SPACE;
                                     objUpdateWO.state=Constant.SPACE;
                                     objUpdateWO.country=Constant.SPACE;
                                     objUpdateWO.postalcode=Constant.SPACE;
                                     lstWOToBeUpdated.add(objUpdateWO);
                                 }
            if(!lstWOToBeUpdated.isEmpty()){
                Database.update(lstWOToBeUpdated);
            }
        }
    }
}