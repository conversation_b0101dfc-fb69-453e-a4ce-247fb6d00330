@isTest
public with sharing class MedwatchFDA3500TriggerHelperTest {
    @isTest
    public static void basicTestInsertAndUpdate() {
        PDLM__Lifecycle__c life = new PDLM__Lifecycle__c(name='Quality Lifecycle Test', PDLM__primary_key__c = 'qualitylifecycletest');
        insert life;
        PDLM__Phase__c initialPhase = new PDLM__Phase__c(Name='Initial', PDLM__order__c = 1, PDLM__Default_No_Change_Control__c = true, PDLM__Is_First_Phase__c = true, PDLM__lifecycle__c = life.id);
        insert initialPhase;
        PDLM__Autonumber__c autoNum = new PDLM__Autonumber__c(name='Autonumber for Quality Test', PDLM__prefix__c = 'QA-Test-', PDLM__Length__c = 3, PDLM__Next_Number__c = 1);
        insert autoNum;
        PDLM__Category__c qCat = new PDLM__Category__c(name='Impacted Product Test', PDLM__Lifecycle__c = life.Id, PDLM__autonumber__c = autoNum.id, PDLM__Type__c = 'Quality');
        insert qCat;
        PDLM__Quality__c quality = new PDLM__Quality__c(PDLM__Title__c='test only', PDLM__Category__c = qCat.Id);
        insert quality;

        Reportable_Authority__c ra = new Reportable_Authority__c();
        ra.Awareness_Date__c = System.today();
        ra.Decision_Tree__c = 'US FDA - Device';
        ra.Regulatory_Authority__c = 'US FDA - Device';
        ra.Quality__c = quality.Id;
        insert ra;

        PLMMW__FDA3500A__c  fdsObj = new PLMMW__FDA3500A__c();
        List<RecordType> rt = [select id, name from RecordType where name = 'Initial Report'];
        fdsObj.recordTypeid = rt[0].id;
        fdsObj.Reportable_Authority_Name__c = ra.id;
        insert fdsObj;
        
        fdsObj.PLMMW__Report_Status__c = 'Complete';
        update fdsObj;
        
        List<RecordType> followupRT = [select id, name,Developername from RecordType where Developername = 'Follow_up_Report'];
       
        PLMMW__FDA3500A__c  followUpObj = new PLMMW__FDA3500A__c();
        followUpObj.recordTypeid = followupRT[0].id;
        followUpObj.Reportable_Authority_Name__c = ra.id;
        insert followUpObj;
        
        followUpObj.PLMMW__Report_Status__c = 'Complete';
        update followUpObj;
        
        PLMMW__FDA3500A__c  followUpObj2 = new PLMMW__FDA3500A__c();
        followUpObj2.recordTypeid = followupRT[0].id;
        followUpObj2.Reportable_Authority_Name__c = ra.id;
        insert followUpObj2;
    }
    
}