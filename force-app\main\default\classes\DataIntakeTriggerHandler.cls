public with sharing class DataIntakeTriggerHandler {
    
   public static void convertTextAndPicklistToUppercase(List<Data_Intake__c> records) {
        // 获取对象所有字段元数据（键：字段API名称，值：字段描述）
        Map<String, Schema.SObjectField> fieldMap = Data_Intake__c.sObjectType.getDescribe().fields.getMap();
        
        for (Data_Intake__c record : records) { // 循环每条记录
            for (String fieldApiName : fieldMap.keySet()) { // 循环所有字段
                Schema.DescribeFieldResult fieldDescribe = fieldMap.get(fieldApiName).getDescribe();
                
                 
                if (isValidFieldType(fieldDescribe)) {
                    String fieldValue = (String) record.get(fieldApiName); // 获取字段值（文本/Picklist均为String）
                    if (fieldValue != null) { // 跳过null值
                        record.put(fieldApiName, fieldValue.toUpperCase()); // 直接转换为大写并写回
                    }
                }
            }
        }
    }
    
    
    private static Boolean isValidFieldType(Schema.DescribeFieldResult fieldDesc) {
        Schema.DisplayType fieldType = fieldDesc.getType(); // 获取字段类型
        
        
        Boolean isTextField = fieldType == Schema.DisplayType.STRING;
        
     
        Boolean isSinglePicklist = (fieldType == Schema.DisplayType.Picklist && !fieldDesc.isMultiSelectPicklist);
        
        return isTextField || isSinglePicklist; // 满足任一条件即返回true
    }
}