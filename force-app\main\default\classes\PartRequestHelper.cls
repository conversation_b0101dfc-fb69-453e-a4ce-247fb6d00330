/**
* <AUTHOR> <PERSON> 
* @date :  28 Dec 2020, Last Modified: NA
* @description: SFDC-864,Helper class to prevent Insertion,Updation,Deletion of PartRequest when parent WorkOrder is closed/completed
*/
public class PartRequestHelper {
    public void preventInsertUpdatePartRequest(List<ProductRequest> lstProdRequest){
        Map<Id,String> mapWO=new Map<Id,String>();
        List<Id> lstWO=new List<Id>();
        for(ProductRequest pr:lstProdRequest)
        {
            if(pr.WorkOrderId!=null){
                lstWO.add(pr.WorkOrderId);
            }
        }
        if(!lstWO.isempty()){
            for(WorkOrder objWO:[Select Id,Status from WorkOrder where ID in:lstWO and Status in:Constant.WORKORDER_STATUS])
            {
                mapWO.put(objWO.Id,objWO.Status);
            }
            for(ProductRequest pr:lstProdRequest)
            {
                if(pr.WorkOrderId!=null && mapWO.containskey(pr.WorkOrderId))
                {
                    pr.addError(Constant.PRT_ERR);
                }
            }
        }
    }
    //SFDC-1109 - Prevent the Part request deletion
    public void preventDeletePartRequest(List<ProductRequest> prDelete) {
        for(ProductRequest pr:prDelete){
        List<ProductRequestLineItem > relatedPRLIList = [Select ParentId from ProductRequestLineItem  Where ParentId=: pr.Id];
        if(relatedPRLIList.size()>0){
            pr.addError('The part request cannot be deleted because it has an associated PRLI. Please set the PRLI status to Canceled instead.');
        }
        }
    }
}