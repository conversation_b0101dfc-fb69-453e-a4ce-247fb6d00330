/**
* <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON> 
* @date :  24 Oct 2019, Last Modified: NA
* @description: FAUXCBUF-2024,Helper class to prevent Insertion,Updation,Deletion of PartRemoved when parent WorkOrder is closed
*/
public class PartRemovedHelper {
    public void preventInsertUpdateWOLI(List<Part_Removed__c> lstProdRemoved){
        Map<Id,String> mapWOLI=new Map<Id,String>();
        List<Id> lstWOLI=new List<Id>();
        for(Part_Removed__c pc:lstProdRemoved)
        {
            lstWOLI.add(pc.Work_Order_Line_Item_Id__c);
        }
        //SFDC-864
        for(WorkOrderLineItem objWOLI:[Select Id,WorkOrder.Status from WorkOrderLineItem where ID in:lstWOLI and WorkOrder.Status in:Constant.WORKORDER_STATUS])
        {
            mapWOLI.put(objWOLI.Id,objWOLI.WorkOrder.Status);
        }
        for(Part_Removed__c pc:lstProdRemoved)
        {
            if(mapWOLI.containskey(pc.Work_Order_Line_Item_Id__c))
            {
                pc.addError(Constant.PR_ERR);
            }
        }
    }    
}