/**
* <AUTHOR> <PERSON>  
* @date :  28 Dec 2020, Last Modified: NA
* @description: SFDC-864,A Test class for PartRequestHelper
*/
@isTest
public class PartRequestHelperTest {
    private static final List<String> ADDRESS=new List<String>{'La Jolla Village Drive','San Diego',
        'California','United States','92161'};
    @testSetup
    /*Setup method to create the Test Records*/
    static void testData() {
        //custom setting creation
        DataFactory.createCustomSetting(Constant.WORKORDERTRIGGER,true);
        DataFactory.createCustomSetting(Constant.WORKORDERLINEITEMTRIGGER,true);
        DataFactory.createCustomSetting(Constant.LOCATIONTRIGGER,false);
        DataFactory.createCustomSetting(Constant.ASSETTRIGGER,false);    
        DataFactory.createCustomSetting(Constant.PARTREQUESTTRIGGER,true);   
        
        //Account creation
        Account objAccount=Datafactory.createAccount();
        
        //case creation
        Case objCase=Datafactory.createCase(objAccount);
        
        //Product creation
        Product2 objProduct=Datafactory.createProduct('Test Prod3');
        
        //Location creation
        Schema.Location objLocation=new Schema.Location();
        objLocation.Name='Testing WOLI';
        objLocation.locationtype='Site';  
        objLocation.IsInventoryLocation=True;
        objLocation.Account__c=objAccount.Id;
        Database.Insert(objLocation);
        
        //Address creation  
        Schema.address objAddress=Datafactory.createAddress(objLocation,ADDRESS[0],ADDRESS[1],ADDRESS[2],ADDRESS[3],ADDRESS[4]);
        objLocation.visitoraddressId=objAddress.Id;
        Database.update(objLocation);            
        
        //Asset creation
        Asset objAsset=Datafactory.createAsset(objAccount, objProduct, objLocation);
        
        //ProductItem Creation
        ProductItem objProItem=new ProductItem();
        objProItem.product2Id=objProduct.Id;
        objProItem.locationId=objLocation.Id;
        objProItem.QuantityOnHand=1.00;
        Database.Insert(objProItem);       
        
    }
    
    @isTest
    //Method To Test preventInsertUpdateWOLI method in PartRemovedHelper Class
    static void preventInsertUpdateTestWOLI(){
      //  try
       // {
            Account objAccount=[SELECT Id FROM Account limit 1];
            Asset objAsset=[SELECT Id FROM Asset limit 1];
            Case objCase=[SELECT Id FROM Case limit 1];
            Product2 pi=[SELECT Id from Product2 Limit 1];
            WorkOrder objWO=Datafactory.createWorkOrder(objAccount, objAsset, objCase);
            WorkType objWorkType = DataFactory.createWorkType('Planned Maintenance');
            WorkOrderLineItem woli=new WorkOrderLineItem();
            woli.WorkOrderId=objWO.id;
            woli.Overtime_Labor_Time_in_Hours__c=1;
            woli.Standard_Labor_Time_in_Hours__c=1;
            woli.Travel_Time_in_Hours__c=1;
            woli.Payment_Method__c='Cost Center'; 
            woli.status='In Progress';
            woli.worktypeId=objWorkType.id;
            woli.assetId=objAsset.Id;
            Database.insert(woli);
           // objWO.status=Constant.COMPLETED;
            //Database.update(objWO);
            ProductRequest PR=DataFactory.createProductReq(objWO);
        //}
      //  catch(Exception error){
        //    System.debug(error.getMessage());
          //  System.Assert(error.getMessage().contains(Constant.PRT_ERR));    
        //}
    }
    
    @isTest
    //Method To Test preventDeleteWOLI method in PartRequestHelper Class
    static void preventDeleteTestWOLI(){
        try{
            Account objAccount=[SELECT Id FROM Account limit 1];
            Asset objAsset=[SELECT Id FROM Asset limit 1];
            Case objCase=[SELECT Id FROM Case limit 1];
            Product2 pi=[SELECT Id from Product2 Limit 1];
            WorkOrder objWO=Datafactory.createWorkOrder(objAccount, objAsset, objCase);
            WorkType objWorkType = DataFactory.createWorkType('Planned Maintenance');
            WorkOrderLineItem woli=new WorkOrderLineItem();
            woli.WorkOrderId=objWO.id;
            woli.Overtime_Labor_Time_in_Hours__c=1;
            woli.Standard_Labor_Time_in_Hours__c=1;
            woli.Travel_Time_in_Hours__c=1;
            woli.Payment_Method__c='Cost Center'; 
            woli.status='Completed';
            woli.worktypeId=objWorkType.id;
            woli.assetId=objAsset.Id;
            Database.insert(woli);
            //objWO.status=Constant.COMPLETED;
            ProductRequest PR=DataFactory.createProductReq(objWO);
            //Database.update(objWO);
            Database.delete(PR);
        }
        
        
        catch(Exception error){
            System.Assert(error.getMessage().contains(Constant.PRT_ERR));    
        }
    }
    
}