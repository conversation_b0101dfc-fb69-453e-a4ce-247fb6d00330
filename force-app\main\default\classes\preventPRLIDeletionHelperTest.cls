/**
* <AUTHOR> <PERSON><PERSON>
* @date :  18 OCT 2023, Last Modified: NA
* @description: SFDC-1109, A Test class for preventPRLIDeletionHelper
*/

@isTest
public class preventPRLIDeletionHelperTest {
    private static final List<String> ADDRESS=new List<String>{'La Jolla Village Drive','San Diego',
        'California','United States','92161'};
            
    @TestSetup
    static void setup(){
        DataFactory.createCustomSetting(Constant.WORKORDERTRIGGER,true);
        DataFactory.createCustomSetting(Constant.WORKORDERLINEITEMTRIGGER,true);
        DataFactory.createCustomSetting(Constant.LOCATIONTRIGGER,false);
        DataFactory.createCustomSetting(Constant.ASSETTRIGGER,false);    
        DataFactory.createCustomSetting(Constant.PARTREQUESTTRIGGER,true); 
        
        Account objAccount=Datafactory.createAccount();        
        Case objCase=Datafactory.createCase(objAccount);
        
        String productFamily = 'AdaptaScope Equipment';
        Product2 product = new Product2();
        product.Name = 'MIR Test Product';
        product.Unique_Product_Code__c = 'MIR_Test_Product';
        product.ProductCode = 'MIR_Test_Product';
        product.Family = productFamily;
        product.IsActive = true;
        insert product;
        
        Schema.Location objLocation=new Schema.Location();
        objLocation.Name='Testing WOLI';
        objLocation.locationtype='Site';  
        objLocation.IsInventoryLocation=True;
        objLocation.Account__c=objAccount.Id;
        Database.Insert(objLocation);
        
        //Address creation  
        Schema.address objAddress=Datafactory.createAddress(objLocation,ADDRESS[0],ADDRESS[1],ADDRESS[2],ADDRESS[3],ADDRESS[4]);
        objLocation.visitoraddressId=objAddress.Id;
        Database.update(objLocation);
        
        Asset objAsset=Datafactory.createAsset(objAccount, product, objLocation);
        WorkOrder objWO=Datafactory.createWorkOrder(objAccount, objAsset, objCase);
        
        ProductRequest PR=DataFactory.createProductReq(objWO);
        ProductRequestLineItem PRLI = new ProductRequestLineItem();
        PRLI.ParentId = PR.Id;
        PRLI.Product2Id = product.Id;
        PRLI.QuantityRequested = 2;
        PRLI.Shipping_Method__c = '3 Day';
        insert PRLI;  
    }
    @isTest
    static void handleDeleteTest() {
        ProductRequestLineItem prli = [SELECT Id,Product2Id,Product2.name FROM ProductRequestLineItem where Product2.name =: 'MIR Test Product'];
        try {
            Database.delete(prli);
        } catch (DmlException e) {
            System.assert(e.getMessage().contains('The PRLI cannot be deleted, so please set the PRLI status to Canceled instead.'));
        }
    }

}