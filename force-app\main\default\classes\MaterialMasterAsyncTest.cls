@isTest
public class MaterialMasterAsyncTest {

    public static final string FILENAME='Same File';
    public static final string CASESTATUS='Intake_In_Progress';
    private static final List<String> ADDRESS=new List<String>{'La Jolla Village Drive','San Diego',
                                      'California','United States','92161'}; 
    @testSetup 
    /*Setup method to create the Test Records*/
    static void testData() {        
        //custom setting creation
        DataFactory.createCustomSetting(Constant.CONTENTDOCTRIGGER,true);
        DataFactory.createCustomSetting(Constant.CONTENTVERTRIGGER,true);
        DataFactory.createCustomSetting(Constant.CONTENTDOCLINKTRIGGER,true);
        DataFactory.createCustomSetting(Constant.WORKORDERTRIGGER,false);
        DataFactory.createCustomSetting(Constant.LOCATIONTRIGGER,false);
        DataFactory.createCustomSetting(Constant.ASSETTRIGGER,false);
        DataFactory.createCustomSetting(Constant.WORKORDERLINEITEMTRIGGER,true);
        DataFactory.createCustomSetting(Constant.RESOLUTIONCODETRIGGER,true);
        DataFactory.createCustomSetting(Constant.SERVICEANALYSISTRIGGER,true);
        //Account creation
        Account objAccount=Datafactory.createAccount();
        
        //Product creation
        Product2 objProduct=Datafactory.createProduct();
        
        //Location creation
        Schema.Location objLocation=Datafactory.createLocation(objAccount);
        
        //Address creation  
        Schema.address objAddress=Datafactory.createAddress(objLocation,ADDRESS[0],ADDRESS[1],ADDRESS[2],ADDRESS[3],ADDRESS[4]);
        objLocation.visitoraddressId=objAddress.Id;
        Database.update(objLocation);
                    
        //Asset creation
        Asset objAsset=Datafactory.createAsset(objAccount, objProduct, objLocation);
        
        //case creation
        Case objCase=Datafactory.createCase(objAccount,CASESTATUS);
        Case objCase1=Datafactory.createCase(objAccount);      
        
        //Work Order Creation
        WorkOrder objWO=Datafactory.createWorkOrder(objAccount, objAsset, objCase);
        Datafactory.createWorkOrder(objAccount, objAsset, objCase1);
        
        WorkType objWorkType = DataFactory.createWorkType('Planned Maintenance');
        WorkOrderLineItem objLineItem = DataFactory.createWOLI(objWO, objWorkType);
        objLineItem.status=Constant.CLOSED;
        Database.update(objLineItem);
   
    }
    
    public class MockHttpResponseGenerator implements HttpCalloutMock {
        // Implement this interface method
        public HTTPResponse respond(HttpRequest req) {
            // Optionally, only send a mock response for a specific endpoint and method.
            System.assertEquals('callout:ASP_PI_MATERIAL_MASTER', req.getEndpoint(), 'Incorrect endpoint, actual is: '+ req.getEndpoint());
            System.assertEquals('POST', req.getMethod(), 'Incorrect method, actual is: '+ req.getMethod());

            // Create a fake response
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'text/xml');
            res.setBody('{"example":"test"}');
            res.setStatusCode(200);
            return res;
        }
    }

    static testMethod void testCalloutSuccess() {
        // Set mock callout class
        Test.startTest();
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator());
        
        //setup
        String versionData = '<ChangeOrders>\n<Details>\n</Details>\n</ChangeOrders>';
        
        // Call method to test.
        // This causes a fake response to be sent
        // from the class that implements HttpCalloutMock
        MaterialMasterAsync.sendCallout(versionData);
        Test.stopTest();
        System.assertEquals(String.isEmpty(versionData), false, 'XML string should contain the xml body');
    }

    static testMethod void testCVTrigger() {
        String versionData = '<ChangeOrders>\n<Details>\n</Details>\n</ChangeOrders>';
        
                Test.startTest();
        
        ContentVersion cv = new ContentVersion();
        cv.Origin = 'H';
        cv.PathOnClient = 'test-attachments.pdf';
        cv.Title = 'test';
        cv.Description = 'Export Item as XML';
        cv.VersionData = Blob.valueOf(versionData);

        insert cv;

        Test.stopTest();

   }
}