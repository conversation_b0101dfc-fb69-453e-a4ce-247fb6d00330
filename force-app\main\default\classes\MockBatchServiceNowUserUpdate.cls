/**********************************************
Description - MockBatchServiceNowUserUpdate[Mock callout for BatchServiceNowUserUpdate]
Created Date - 20-10-2022
Created by - <PERSON><PERSON>s
**********************************************/
@isTest
global class MockBatchServiceNowUserUpdate implements HttpCalloutMock {
    
    // Implement this interface method
    global HTTPResponse respond(HTTPRequest req) {
     
        // Create a fake response
        HttpResponse res = new HttpResponse();
        res.setHeader('Content-Type', 'application/json');
        res.setBody('{"result":[{"firstname":"<PERSON>hy<PERSON> (<PERSON>)","mangerfortiveid":"556026","businesstitle":"Import Lead","active":"true","job_function.job_function":"Shipping/Receiving & Distribution","sys_updated_on":"2022-10-20 04:50:48","startdate":"2020-04-20","display_name":"<PERSON>, <PERSON><PERSON><PERSON> (<PERSON>) (556035 / <EMAIL>)","lastname":"Kim","hr_manager.display_name":"Lee, Chulkyu (CK) (556026 / <EMAIL>)","number":"MYFID03798","employee_type":"employee","sys_updated_by":"ftv_myfortive","sys_created_on":"2020-11-07 21:51:51","u_fortiveid":"990821","funtional_department.name":"Quality","primaryemail":"<EMAIL>"}]}');
        res.setStatusCode(200);
        return res;
        
        
    }
}