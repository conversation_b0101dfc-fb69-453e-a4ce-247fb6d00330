/**
* <AUTHOR> <PERSON>ure<PERSON>  
* @date :  20 Sept 2019, Last Modified: NA
* @description: FAUXCBUF-1968,A Test class for LocationHelper
*/
@isTest
private class LocationHelperTest {
        private static final List<String> ADDRESS=new List<String>{'La Jolla Village Drive','San Diego',
                                                                'California','United States','92161'};
        private static final List<String> UPDADDRESS=new List<String>{'Main Street','Longmont','Colorado','United States','80501'};
            @testSetup 
            /*Setup method to create the Test Records*/
            static void testData() {
                //custom setting creation
                DataFactory.createCustomSetting(Constant.LOCATIONTRIGGER,true);
                DataFactory.createCustomSetting(Constant.ASSETTRIGGER,false);
                DataFactory.createCustomSetting(Constant.WORKORDERTRIGGER,false);                  
                //Account creation
                Account objAccount=Datafactory.createAccount();
                
                //case creation
                Case objCase=Datafactory.createCase(objAccount);
                
                //Product creation
                Product2 objProduct=Datafactory.createProduct();
                
                //Location creation
                Schema.Location objLocation=Datafactory.createLocation(objAccount);
                
                //Address creation  
                Schema.address objAddress=Datafactory.createAddress(objLocation,ADDRESS[0],ADDRESS[1],ADDRESS[2],ADDRESS[3],ADDRESS[4]);
                Schema.address objAddress1=Datafactory.createAddress(objLocation,UPDADDRESS[0],UPDADDRESS[1],
                                                                     UPDADDRESS[2],UPDADDRESS[3],UPDADDRESS[4]);
                
                objLocation.visitoraddressId=objAddress.Id;
                Database.update(objLocation);
                
                //Asset creation
                Asset objAsset=Datafactory.createAsset(objAccount, objProduct, objLocation);
                
                //Work Order Creation
                Datafactory.createWorkOrder(objAccount, objAsset, objCase);
            }
    @isTest
    /*Method To Test updateRelatedWOAddress and populateLocationCountryCode method in LocationHelper Class*/
    static void updateRelatedWOAddressTest(){       
        system.runAs(DataFactory.createUser()){
            Test.startTest();            
            List<Schema.Location> lstLocation=[SELECT Id,VisitoraddressId
                                               FROM Location LIMIT 1];
            List<Schema.Address> lstAddress=[SELECT Id 
                                             FROM Address 
                                             WHERE state='Colorado' LIMIT 1];
            if(!lstLocation.isEmpty()){
                lstLocation[0].visitorAddressId=lstAddress[0].Id;       
                Database.update(lstLocation);
                List<Schema.Location> lstUpdtLocation=[SELECT Id,Visitoraddress.street,Visitoraddress.city,Visitoraddress.state,
                                                       Visitoraddress.country,Visitoraddress.postalcode,Address_Country_Code__c
                                                       FROM Location LIMIT 1];
                List<WorkOrder> lstWorkOrder=[SELECT Id,street,city,state,country,postalcode,countrycode
                                              FROM WorkOrder 
                                              WHERE WorkOrder.Asset.locationId=:lstLocation[0].Id LIMIT 1];
                Test.stopTest();
                System.assertEquals(lstUpdtLocation[0].Visitoraddress.street,lstWorkOrder[0].street);
                System.assertEquals(lstUpdtLocation[0].Visitoraddress.city,lstWorkOrder[0].city);
                System.assertEquals(lstUpdtLocation[0].Visitoraddress.state,lstWorkOrder[0].state);
                System.assertEquals(lstUpdtLocation[0].Visitoraddress.country,lstWorkOrder[0].country);
                System.assertEquals(lstUpdtLocation[0].Visitoraddress.postalcode,lstWorkOrder[0].postalcode);
                System.assertEquals(lstUpdtLocation[0].Address_Country_Code__c,lstWorkOrder[0].CountryCode);                
            }
        }       
    }
    
    @isTest
    /*Method To Test clearWOAddress method in LocationHelper Class*/
    static void clearWOAddressTest(){        
        system.runAs(DataFactory.createUser()){ 
            Test.startTest();
            List<Schema.Location> lstLocation=[SELECT Id,VisitoraddressId,Address_Country_Code__c
                                               FROM Location LIMIT 1];
            if(!lstLocation.isEmpty()){
                //lstLocation[0].visitorAddressId=null;
                //Database.update(lstLocation);
                List<WorkOrder> lstWorkOrder=[SELECT Id,street,city,state,country,postalcode
                                              FROM WorkOrder 
                                              WHERE WorkOrder.Asset.locationId=:lstLocation[0].Id LIMIT 1];
                Test.stopTest();
                System.assertEquals(null,lstWorkOrder[0].Street);
                System.assertEquals(null,lstWorkOrder[0].city);
                System.assertEquals(null,lstWorkOrder[0].state);
                System.assertEquals(null,lstWorkOrder[0].country);
                System.assertEquals(null,lstWorkOrder[0].postalcode);
                //System.assertEquals(null,[SELECT Id,Address_Country_Code__c
                                               //FROM Location LIMIT 1].Address_Country_Code__c);
            } 
        }       
    }
}