@isTest
public class PrimaryFSEBatchTest {
     private static final List<String> ADDRESS=new List<String>{'La Jolla Village Drive','San Diego',
        'California','United States','92161'};
    @testSetup
    static void setup(){
        DataFactory.createCustomSetting(Constant.WOLITRIGGER,true);
        DataFactory.createCustomSetting(Constant.WORKORDERTRIGGER,true);
        DataFactory.createCustomSetting(Constant.WORKORDERLINEITEMTRIGGER,true);
        DataFactory.createCustomSetting(Constant.LOCATIONTRIGGER,true);
        DataFactory.createCustomSetting(Constant.ASSETTRIGGER,true);
        DataFactory.createCustomSetting(Constant.PARTCONSUMEDTRIGGER,true);
        DataFactory.createCustomSetting(Constant.PARTREMOVEDTRIGGER,true);
        //Account Creation
        Account objAccount=Datafactory.createAccount();
        
        //Product creation
        Product2 objProduct=Datafactory.createProduct('Test Prod'); 
        
        //Location creation
        Schema.Location objLocation=new Schema.Location();
        objLocation.Name='Colorado Location';
        objLocation.Locationtype='Site';
        objLocation.IsInventoryLocation=true;
        objLocation.Account__c=objAccount.id;
        Database.Insert(objLocation);
        
        //Polygon Creation
        string XMLString;
        
        /* FSL__Polygon__c pol = new FSL__Polygon__c();
pol.FSL__KML__c='<?xml version="1.0" encoding="UTF-8"?> <kml xmlns="http://www.opengis.net/kml/2.2"><Style id="myPolygonStyle"> <LineStyle> <width>1</width> </LineStyle> <PolyStyle> <color>8047A043</color> </PolyStyle> </Style> <Placemark> <name>myPolygon</name> <styleUrl>#myPolygonStyle</styleUrl> <Polygon> <outerBoundaryIs> <LinearRing><coordinates>-122.**************,37.**************,0-122.*************,37.***************,0-122.*************,37.**************,0-122.**************,37.**************,0-122.**************,37.**************,0-122.**************,37.**************,0-122.**************,37.**************,0-122.*************,37.**************,0-122.**************,37.**************,0</coordinates> </LinearRing> </outerBoundaryIs> </Polygon> </Placemark> </kml>';        
insert pol;*/    
        
        
        //Database.insert(pol);
        
        //OperatingHours Creation
        OperatingHours oh=new OperatingHours();
        oh.Name='9amto5pm';
        Database.Insert(oh);
        
        //Service Territory Creation
        ServiceTerritory st =new ServiceTerritory();
        st.Name='SpringField';
        st.OperatingHoursId=oh.Id;
        
        st.IsActive=true;
        Database.insert(st);
        
        //Service Resource creation
        ServiceResource sr=new ServiceResource();
        sr.Name='Allen Cartner';
        List<User> user=[Select Id from User where isActive=true LIMIT 1];
        sr.RelatedRecordId=user[0].id;
        sr.Service_Resource_Country_Code__c='100';
        sr.IsActive=true;
        Database.insert(sr);
        
        //Service Territory Member Creation
        ServiceTerritoryMember stm=new ServiceTerritoryMember();
        stm.ServiceResourceId=sr.Id;
        stm.ServiceTerritoryId=st.id;
        stm.EffectiveStartDate=System.today()-1;
        stm.EffectiveEndDate=System.today()+30; 
        stm.TerritoryType = 'P';
        
        Database.insert(stm);
        
        //Address creation  
        Schema.address objAddress=Datafactory.createAddress(objLocation,ADDRESS[0],ADDRESS[1],ADDRESS[2],ADDRESS[3],ADDRESS[4]);      
        objLocation.visitoraddressId=objAddress.Id;
        Database.update(objLocation);        
        
        //Asset Creation
        Asset objAsset=Datafactory.createAsset(objAccount, objProduct, objLocation);
    }
    
    @isTest
    static void BatchTest(){
       
        List<ServiceTerritory> st=[select Id from ServiceTerritory Limit 1];
        System.debug('----st------::'+st);
        //Database.update(st);
       
        //Testcase1        
        List<Asset> asset1 = [select Id from Asset Limit 1];
        asset1[0].Primary_Field_Service_Engineer__c=NULL;
        asset1[0].Service_Territory__c=st[0].Id;
        Database.update(asset1);
        
        List<ServiceTerritoryMember> stm=[select Id,ServiceTerritoryId,TerritoryType,EffectiveStartDate,EffectiveEndDate,ServiceResource.IsActive,ServiceResourceId from ServiceTerritoryMember Limit 1];
        System.debug('stm-------::'+stm);
        //stm[0].ServiceTerritoryId=st[0].Id;
        //stm[0].TerritoryType=Constant.TerrType; 
        //stm[0].EffectiveStartDate=System.today()-1;
        //stm[0].EffectiveEndDate=NULL;
        //Database.update(stm);
        
        system.debug('STM Test class:'+stm);
        
        //Testcase2
       
        Test.startTest();
        
        PrimaryFSEBatch obj = new PrimaryFSEBatch();
        DataBase.executeBatch(obj); 
        
        Test.stopTest();
        
        
        
    }
}