/**
 * <AUTHOR>
 * @date        April 24, 2020
 * @description Test Class for NoteContentDocumentTriggerHandler
 */

@isTest
public class NoteContentDocumentTriggerHandlerTest {
    

    @isTest(SeeAllData=true)
    
    public static void testNoteInsert(){
         List<Recordtype> accountRT = [select Id,Name from RecordType where sObjectType='Account' and Name = 'Prospect'];
        List<Recordtype> oppRT = [select Id,Name from RecordType where sObjectType='Opportunity' and Name ='Capital - Bid & Tender'];

        List<Account> accountList = new List<Account>();
        account acc = new Account();
        acc.Name = 'test Account';
        acc.ShippingCountryCode = 'US';
        acc.ShippingStateCode = 'AL';
        acc.ShippingPostalCode = '35007';
        acc.RecordTypeId = accountRT[0].Id;
        accountList.add(acc);
        insert accountList;
        
        List<Opportunity> oppList = new List<Opportunity>();
        Opportunity opp = new Opportunity();
        opp.Name = 'test Opp';
        opp.AccountId = accountList[0].Id;
        opp.StageName = '0 - Targeting';
        opp.Amount = 1;
        opp.Type = 'New Construction/Project';
        opp.CloseDate = date.today();
        opp.Business_Model__c = 'Sale';
        opp.Probability = 25;
            oppList.add(opp);
        insert oppList;
        
        ContentNote cn = new ContentNote(
            Title = 'test Note',
            Content = Blob.valueOf('Test Content')
        );
        insert cn ;   
        
        //create ContentDocumentLink  record 
        ContentDocumentLink cdl = New ContentDocumentLink();
        cdl.LinkedEntityId = oppList[0].Id; 
        cdl.ContentDocumentId = cn.Id;
        cdl.shareType = 'V';
        insert cdl;
        
        List<Opportunity> oppObj = [Select  lastmodifieddate from Opportunity where id =: cdl.LinkedEntityId ];
       List<ContentDocumentLink> cdlList = [SELECT LinkedEntityId, ContentDocumentId, systemmodstamp FROM ContentDocumentLink WHERE LinkedEntityID =: cdl.LinkedEntityId];
      
        //system.assertEquals(cdlList[0].systemmodstamp, oppObj[0].last_modified_date_stamp__c);
        
        
    }
      

}