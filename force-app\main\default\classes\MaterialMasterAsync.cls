public class MaterialMasterAsync implements Queueable, Database.AllowsCallouts {

    ContentVersion[] cvs;

    public MaterialMasterAsync(List<String> cvIds) {
        this.cvs = [select Id, VersionData from ContentVersion where Id in :cvIds];
    }

    public void execute(QueueableContext context) {
        for (ContentVersion cv : this.cvs) {
            sendCallout(cv.VersionData.toString());
        }
    }

    public static void sendCallout(String versionData) {
        String xmlBody = getXMLBody(versionData);

        HttpRequest req = new HttpRequest();
        req.setEndpoint('callout:ASP_PI_MATERIAL_MASTER');
        req.setMethod('POST');
        req.setBody(xmlBody);
        req.setTimeout(60*1000);
        req.setHeader('Content-type', 'text/xml');
        req.setHeader('Content-Length', xmlBody.length().format());

        try {
            Http h = new Http();
            HttpResponse res = h.send(req);
            System.debug(res.getStatusCode());
        } catch (Exception e) {
            System.debug(e);
        }
    }

    @TestVisible
    private static String getXMLBody(String versionData) {
        List<String> cvList = versionData.split('\n');
        cvList.remove(0);
        String body = String.join(cvList, '\n');
        XmlStreamWriter w = new XmlStreamWriter();
        w.writeStartDocument('UTF-8', '1.0');
        w.writeStartElement('soapenv', 'Envelope', 'http://schemas.xmlsoap.org/soap/envelope/');
        w.writeNamespace('soapenv', 'http://schemas.xmlsoap.org/soap/envelope/');
        w.writeEmptyElement('soapenv', 'Header', '');
        w.writeStartElement('soapenv', 'Body', '');

        String xmlOutput = w.getXmlString();
        xmlOutput += '>' + String.join(cvList, '');
        w.close();

        w = new XmlStreamWriter();

        w.writeStartElement('soapenv', 'Envelope', 'http://schemas.xmlsoap.org/soap/envelope/');
        w.writeNamespace('soapenv', 'http://schemas.xmlsoap.org/soap/envelope/');
        w.writeStartElement('soapenv', 'Body', '');
        w.writeEndElement(); // Body
        w.writeEndElement(); // Envelope
        String xmlOutputTail = w.getXmlString();
        w.close();

        xmlOutputTail = xmlOutputTail.substring(xmlOutputTail.indexOf('>') + 1);
        xmlOutputTail = xmlOutputTail.substring(xmlOutputTail.indexOf('>') + 1);

        xmlOutput += xmlOutputTail;

        return xmlOutput;
    }
}