public class MedwatchFDA3500TriggerHelper{
    private static Set<Id> initTypeIds;
    private static Set<Id> followTypeIds;

    public static void autoGenerateMfrReportNumber(List<PLMMW__FDA3500A__c> triggerNewList) {
        setRecordType();
        PLMMW__FDA3500A__c[] newInitReports = new PLMMW__FDA3500A__c[]{};
        PLMMW__FDA3500A__c[] newFollowupReports = new PLMMW__FDA3500A__c[]{};
        for (PLMMW__FDA3500A__c report : triggerNewList) {
            if(report.PLMMW__Legacy_Report__c || report.PLMMW__G_Manufacturer_Report_Number__c != null){
                continue;
            }
            if (initTypeIds.contains(report.RecordTypeId)) {
                newInitReports.add(report);
            } else {
                newFollowupReports.add(report);
            }
        }

        autoGenerateMfrReportNumberInit(newInitReports);
        autoGenerateMfrReportNumberFollowup(newFollowupReports);

    }

    private static void autoGenerateMfrReportNumberInit(PLMMW__FDA3500A__c[] newInitReports) {
        if (newInitReports == null || newInitReports.size() == 0) { return; }

        // get next number
        PDLM__Configuration__c prefixConfig = PDLM__Configuration__c.getValues('FDAPrefix');
        String prefix = (prefixConfig != null ? prefixConfig.PDLM__Value__c : '') + '-' + System.Today().year();
        Integer yearReportCount = 1;

        Reportable_Authority__c[] lastRAs = [
            select Id, Manufacturer_Report_Number__c
            from Reportable_Authority__c
            where Manufacturer_Report_Number__c like :(prefix + '%')
            order by Manufacturer_Report_Number__c desc limit 1];

        if (lastRAs != null && lastRAs.size() > 0) {
            String lastReportNum = lastRAs[0].Manufacturer_Report_Number__c;
            if(String.isNotBlank(lastReportNum)){
                String[] lastReportNumPart = lastReportNum.split('-');
                yearReportCount = Integer.valueOf(lastReportNumPart[2]) + 1;
            }
        }

        Set<Id> parentRAIds = new Set<Id>();
        for (PLMMW__FDA3500A__c fReport : newInitReports) {
            parentRAIds.add(fReport.Reportable_Authority_Name__c);
        }
        Map<Id,Reportable_Authority__c> parentRAs = new Map<Id,Reportable_Authority__c>([
            select Id, Manufacturer_Report_Number__c, (select Id from MedWatch_3500A1__r limit 1)  from Reportable_Authority__c where Id in :parentRAIds]);
        for (PLMMW__FDA3500A__c initReport : newInitReports) {
            Reportable_Authority__c parentRA = parentRAs.get(initReport.Reportable_Authority_Name__c);
            if (String.isNotBlank(parentRA.Manufacturer_Report_Number__c) && parentRA.MedWatch_3500A1__r != null && parentRA.MedWatch_3500A1__r.size() > 0) {
                // reuse number from RA
                initReport.PLMMW__G_Manufacturer_Report_Number__c = parentRA.Manufacturer_Report_Number__c;
            } else {
                // use new number and save it back to RA
                String suffix = String.valueOf(yearReportCount).leftPad(5, '0');
                initReport.PLMMW__G_Manufacturer_Report_Number__c = prefix + '-' + suffix;
                parentRA.Manufacturer_Report_Number__c = initReport.PLMMW__G_Manufacturer_Report_Number__c;
                yearReportCount += 1;
            }
        }

        update parentRAs.values();
    }

    private static void autoGenerateMfrReportNumberFollowup(PLMMW__FDA3500A__c[] newFollowupReports) {
        if (newFollowupReports == null || newFollowupReports.size() == 0) { return; }
        Map<Id,Integer> followupCountMap = new Map<Id,Integer>();
        Set<Id> parentReportIds = new Set<Id>();
        for (PLMMW__FDA3500A__c fReport : newFollowupReports) {
            parentReportIds.add(fReport.PLMMW__Follow_up_of_Report__c);
        }
        //QMS-3768
        Map<Id,PLMMW__FDA3500A__c> parentReportMap = new Map<Id,PLMMW__FDA3500A__c>([select Id, PLMMW__G_Manufacturer_Report_Number__c, (select Id, PLMMW__G_If_Follow_up_Follow_up_Num__c from PLMMW__MedWatch_3500A_Reports__r where PLMMW__G_If_Follow_up_Follow_up_Num__c != null AND PLMMW__Report_Status__c != 'Void' AND (RecordType.Developername='Follow_up_Report' OR RecordType.Developername='Follow_up_Report_Locked') ORDER BY CreatedDate DESC limit 1) from PLMMW__FDA3500A__c where Id in :parentReportIds and PLMMW__Report_Status__c != 'Void']);
        for (PLMMW__FDA3500A__c fReport : newFollowupReports) {
            PLMMW__FDA3500A__c parentReport = parentReportMap.get(fReport.PLMMW__Follow_up_of_Report__c);

            if (parentReport == null) {
                fReport.addError('Cannot create Followup report without an Initial_Report');
                continue;
            }

            Integer version = 1;
            if (followupCountMap.containsKey(parentReport.Id)) {
                version = followupCountMap.get(parentReport.Id) + 1;
            } else if (parentReport.PLMMW__MedWatch_3500A_Reports__r != null && parentReport.PLMMW__MedWatch_3500A_Reports__r.size() > 0) {
                PLMMW__FDA3500A__c latestFReport = parentReport.PLMMW__MedWatch_3500A_Reports__r[0];
                version = Integer.valueOf(latestFReport.PLMMW__G_If_Follow_up_Follow_up_Num__c) + 1;
            }
            followupCountMap.put(parentReport.Id, version);
            fReport.PLMMW__G_If_Follow_up_Follow_up_Num__c = version + '';
            fReport.PLMMW__G_Manufacturer_Report_Number__c = parentReport.PLMMW__G_Manufacturer_Report_Number__c;
        }
    }

    public static void validateReportCreation(List<PLMMW__FDA3500A__c> triggerNewList){
        setRecordType();
        for (PLMMW__FDA3500A__c currFda : triggerNewList) {
            if (initTypeIds.contains(currFda.RecordTypeId)) {
                List<PLMMW__FDA3500A__c> fdaExisting = [select id from PLMMW__FDA3500A__c where recordtypeid in :initTypeIds and Reportable_Authority_Name__c = :currFda.Reportable_Authority_Name__c and PLMMW__Report_Status__c != 'Void'];
                if(fdaExisting.size() > 0){
                    currFda.addError('This Reportable authority already has an Initial_Report associated to it. Please create a followup report or a new report on a different RA');
                }
            } else {
                List<PLMMW__FDA3500A__c> fdaExisting = [select id, PLMMW__Report_Status__c from PLMMW__FDA3500A__c where Reportable_Authority_Name__c = :currFda.Reportable_Authority_Name__c and PLMMW__Report_Status__c != 'Void' order by createdDate desc];
                if (fdaExisting != null && fdaExisting.size() > 0) {
                    if (fdaExisting[0].PLMMW__Report_Status__c != 'Complete') {
                        currFda.addError('Error: An unsubmitted follow-up already exists. Only one pending follow-up is allowed.');
                    }
                }
            }
            if (currFda.Reportable_Authority_Name__c == null) {
                currFda.addError('There is no reportable authority associated to this report');
            }
        }
    }

    public static void autoPopulateFollowUp(List<PLMMW__FDA3500A__c> triggerNewList){
        setRecordType();
        for (PLMMW__FDA3500A__c currFda : triggerNewList) {
            if (followTypeIds.contains(currFda.RecordTypeId)) {
                List<PLMMW__FDA3500A__c> fdaExisting = [select id from PLMMW__FDA3500A__c where recordtypeid in :initTypeIds and Reportable_Authority_Name__c = :currFda.Reportable_Authority_Name__c and PLMMW__Report_Status__c != 'Void'];
                if (fdaExisting.size() == 0) {
                    currFda.addError('Cannot create Followup report without Initial_Report');
                } else {
                    currFda.PLMMW__Follow_up_of_Report__c = fdaExisting[0].id;
                }
            }
        }
    }

    public static void validateVoiding(List<PLMMW__FDA3500A__c> triggerNewList, List<PLMMW__FDA3500A__c> triggerOldList){
        setRecordType();
        for (PLMMW__FDA3500A__c currFda : triggerNewList) {
            List<PLMMW__FDA3500A__c> fdaExisting = [select id from PLMMW__FDA3500A__c where recordtypeid in :followTypeIds and Reportable_Authority_Name__c = :currFda.Reportable_Authority_Name__c and PLMMW__Report_Status__c != 'Void'];
            if (initTypeIds.contains(currFda.RecordTypeId)) {
                if (currFda.PLMMW__Report_Status__c == 'Void' && triggerOldList[0].PLMMW__Report_Status__c != 'Void' && fdaExisting.size() != 0) {
                    currFda.addError('Cannot Void an Initial_Report that has followup reports');
                }
            }
        }
    }

    private static void setRecordType() {
        if (initTypeIds != null && followTypeIds != null) { return; }
        initTypeIds = new Set<Id>();
        followTypeIds = new Set<Id>();
        Map<Id,RecordType> recordTypeMap = new Map<Id,RecordType>([select Id, DeveloperName from RecordType where NamespacePrefix = 'PLMMW']);
        for (Id rid: recordTypeMap.keySet()) {
            if(recordTypeMap.get(rid).DeveloperName == 'Initial_Report' || recordTypeMap.get(rid).DeveloperName == 'Initial_Report_Locked' ) {
                initTypeIds.add(rid);
            }
            if(recordTypeMap.get(rid).DeveloperName == 'Follow_up_Report' || recordTypeMap.get(rid).DeveloperName == 'Follow_up_Report_Locked' ) {
                followTypeIds.add(rid);
            }
        }
    }

}//fin