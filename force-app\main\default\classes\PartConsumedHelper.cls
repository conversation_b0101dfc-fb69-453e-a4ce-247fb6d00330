/**
* <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON> 
* @date :  24 Oct 2019, Last Modified: NA
* @description: FAUXCBUF-2024,Helper class to prevent Insertion,Updation,Deletion of PartConsumed when parent WorkOrder is closed
*/
public class PartConsumedHelper {
    public void preventInsertUpdateWOLI(List<ProductConsumed> lstProdconsumed){
        Map<Id,String> mapWOLI=new Map<Id,String>();
        List<Id> lstWOLI=new List<Id>();
        for(ProductConsumed pc:lstProdConsumed)
        {
            lstWOLI.add(pc.WorkOrderLineItemId);
        }
        //SFDC-864
        for(WorkOrderLineItem objWOLI:[Select Id,WorkOrder.Status from WorkOrderLineItem where ID in:lstWOLI and WorkOrder.Status in:Constant.WORKORDER_STATUS])
        {
            
            mapWOLI.put(objWOLI.Id,objWOLI.WorkOrder.Status);
        }
        for(ProductConsumed prodc:lstProdConsumed)
        {
            if(mapWOLI.containskey(prodc.WorkOrderLineItemId))
            {
                prodc.addError(Constant.PC_ERR);
            }
        }
    }    
}