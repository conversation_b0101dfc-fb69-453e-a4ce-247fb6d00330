/**
* <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>  
* @date :  24 Oct 2019, Last Modified: NA
* @description: FAUXCBUF-2024,A Test class for PartRemovedHelper
*/
@isTest
public class PartRemovedHelperTest {
    private static final List<String> ADDRESS=new List<String>{'La Jolla Village Drive','San Diego',
        'California','United States','92161'};
    @testSetup
    /*Setup method to create the Test Records*/
    static void testData() {
        //custom setting creation
        DataFactory.createCustomSetting(Constant.WORKORDERTRIGGER,true);
        DataFactory.createCustomSetting(Constant.WORKORDERLINEITEMTRIGGER,true);
        DataFactory.createCustomSetting(Constant.LOCATIONTRIGGER,false);
        DataFactory.createCustomSetting(Constant.ASSETTRIGGER,false);
        DataFactory.createCustomSetting(Constant.PARTREMOVEDTRIGGER,true);
        DataFactory.createCustomSetting(Constant.RESOLUTIONCODETRIGGER,true);
        DataFactory.createCustomSetting(Constant.SERVICEANALYSISTRIGGER,true);     
        
        //Account creation
        Account objAccount=Datafactory.createAccount();
        
        //case creation
        Case objCase=Datafactory.createCase(objAccount);
        
        //Product creation
        Product2 objProduct=Datafactory.createProduct('Test Prod3');
        
        //Location creation
        Schema.Location objLocation=new Schema.Location();
        objLocation.Name='Testing WOLI';
        objLocation.locationtype='Site';  
        objLocation.IsInventoryLocation=True;
        objLocation.Account__c=objAccount.Id;
        Database.Insert(objLocation);
        
        //Address creation  
        Schema.address objAddress=Datafactory.createAddress(objLocation,ADDRESS[0],ADDRESS[1],ADDRESS[2],ADDRESS[3],ADDRESS[4]);
        objLocation.visitoraddressId=objAddress.Id;
        Database.update(objLocation);            
        
        //Asset creation
        Asset objAsset=Datafactory.createAsset(objAccount, objProduct, objLocation);
        
        //ProductItem Creation
        ProductItem objProItem=new ProductItem();
        objProItem.product2Id=objProduct.Id;
        objProItem.locationId=objLocation.Id;
        objProItem.QuantityOnHand=1.00;
        Database.Insert(objProItem);       
        
    }
    
    @isTest
    //Method To Test preventInsertUpdateWOLI method in PartRemovedHelper Class
    static void preventInsertUpdateTestWOLI(){
        try
        {
            Account objAccount=[SELECT Id FROM Account limit 1];
            Asset objAsset=[SELECT Id FROM Asset limit 1];
            Case objCase=[SELECT Id FROM Case limit 1];
            Product2 pi=[SELECT Id from Product2 Limit 1];
            WorkOrder objWO=Datafactory.createWorkOrder(objAccount, objAsset, objCase);
            WorkType objWorkType = DataFactory.createWorkType('Planned Maintenance');
            WorkOrderLineItem woli=new WorkOrderLineItem();
            woli.WorkOrderId=objWO.id;
            woli.Overtime_Labor_Time_in_Hours__c=1;
            woli.Standard_Labor_Time_in_Hours__c=1;
            woli.Travel_Time_in_Hours__c=1;
            woli.Payment_Method__c='Cost Center'; 
            woli.status=Constant.COMPLETED;
            woli.worktypeId=objWorkType.id;
            woli.assetId=objAsset.Id;
            Database.insert(woli);
            Code__c objCode=DataFactory.createCode('Service Analysis Code');
            Code__c objCode1=DataFactory.createCode('Resolution Code');
            DataFactory.createResolutionCode(objWO,woli,objCode1);
            DataFactory.createServiceAnalysis(objWO,woli,objCode);
            objWO.status=Constant.CLOSED;
            objWO.Asset_Specification_Status__c='Unit does not meet specs';
            Database.update(objWO);
            Part_Removed__c pr=new Part_Removed__c();
            pr.work_Order__c=objWO.Id;
            pr.work_order_Line_Item_Id__c=woli.Id;
            pr.product2Id__c=pi.Id;
            Database.insert(pr);
        }
        catch(Exception error){
            System.Assert(error.getMessage().contains(Constant.PR_ERR));    
        }
    }
    
    @isTest
    //Method To Test preventDeleteWOLI method in PartRemovedHelper Class
    static void preventDeleteTestWOLI(){
        try{
            Account objAccount=[SELECT Id FROM Account limit 1];
            Asset objAsset=[SELECT Id FROM Asset limit 1];
            Case objCase=[SELECT Id FROM Case limit 1];
            Product2 pi=[SELECT Id from Product2 Limit 1];
            WorkOrder objWO=Datafactory.createWorkOrder(objAccount, objAsset, objCase);
            WorkType objWorkType = DataFactory.createWorkType('Planned Maintenance');
            WorkOrderLineItem woli=new WorkOrderLineItem();
            woli.WorkOrderId=objWO.id;
            woli.Overtime_Labor_Time_in_Hours__c=1;
            woli.Standard_Labor_Time_in_Hours__c=1;
            woli.Travel_Time_in_Hours__c=1;
            woli.Payment_Method__c='Cost Center';     
            woli.status=Constant.COMPLETED;
            woli.worktypeId=objWorkType.id;
            woli.assetId=objAsset.Id;
            Database.insert(woli);
            Part_Removed__c pr=new Part_Removed__c();
            pr.work_Order__c=objWO.Id;
            pr.work_order_Line_Item_Id__c=woli.Id;
            pr.product2Id__c=pi.Id;
            Database.insert(pr); 
            Code__c objCode=DataFactory.createCode('Service Analysis Code');
            Code__c objCode1=DataFactory.createCode('Resolution Code');
            DataFactory.createResolutionCode(objWO,woli,objCode1);
            DataFactory.createServiceAnalysis(objWO,woli,objCode);
            objWO.status=Constant.CLOSED;
            objWO.Asset_Specification_Status__c='Unit does not meet specs';
            Database.update(objWO);
            Database.delete(pr);
        }
        catch(Exception error){
            System.Assert(error.getMessage().contains(Constant.PR_ERR));    
        }
    }
}