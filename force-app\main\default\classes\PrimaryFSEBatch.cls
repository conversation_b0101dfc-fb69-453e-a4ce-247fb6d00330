/* -----------------------------------------------------------------------------------------------------------------------
   Name:        PrimaryFSEBatch
   Description: This class is called to update the FSEs on assets.

   Date         Version  Author                Summary of Changes 
   -----------  -------  -----------------     ------------------------------------------------------------------------------
   24/01/2020     1.0    Santhanalakshmi      FAUXCBUF-2406, Batch Apex class to populate Primary FSE and Service Territory in Asset
   06/01/2022     2.0    Himanshu             SFDC-3191, Optimized the code
   05/02/2023     2.0    Himanshu             SFDC-3315, added the logic for active FSE and updating the logic to accomodate the changes for wrongly inserted FSE details
   12/09/2023     3.0    Ravi                 SFDC-3315, updating the logic to accomodate the changes for wrongly inserted FSE details or blank FSE
   03/01/2024     4.0    Ravi                 SFDC-3941, Update the expired FSE with latest primary FSE
   28/02/2024     5.0    Ravi                 SFDC-4331, Do not remove the Primary FSE and Service Territory if there is no service territory in the system based on polygons.
-------------------------------------------------------------------------------------------------------------------------- */
global class PrimaryFSEBatch implements Database.Batchable<sObject>{    
    /*Method to get the list of assets to be updated*/
    global Iterable<SObject> start(Database.BatchableContext bc){
        try{
            List<Asset> assetFSEUpdate = new List<Asset>();
            List<String> stlist=new List<String>();
            List<Sobject> updatedLocation = new List<Sobject>();            
            Set<Sobject> updatedLocationSet = new Set<Sobject>();
            Set<String> serTerritorySet=new Set<String>();
            set<String> setAssetId = new set<String>();
            List<String> uId = System.Label.PrimaryFSEBatch_Excluded_UserIds.split(',');

            for (AssetHistory a: [select AssetId from AssetHistory 
                 where (CreatedById NOT IN: uId) 
                 and CreatedDate >=YESTERDAY 
                 and Field IN('Service_Territory__c', 'Primary_Field_Service_Engineer__c')]) {
            	setAssetId.add(a.AssetId);
            }

            for(FSL__Polygon__c pol:[SELECT Id, FSL__Service_Territory__c FROM FSL__Polygon__c where LastModifiedDate >= Yesterday]){
                stlist.add(pol.FSL__Service_Territory__c);
            }

            serTerritorySet.addAll(stlist);
            List<Sobject> address = [SELECT Id, LastModifiedDate FROM Address where LastModifiedDate >= Yesterday];
            updatedLocation = [SELECT Id FROM Location where VisitorAddressId in :address OR LastModifiedDate >= Yesterday];
            updatedLocationSet.addAll(updatedLocation);

            List<String> serTerritory = new List<String>();
            
            for(ServiceTerritoryMember stm:[SELECT ServiceTerritoryId,Id FROM ServiceTerritoryMember 
                                            where TerritoryType=:Constant.TerrType 
                                            AND (EffectiveEndDate > Yesterday OR EffectiveEndDate = NULL)]){
                serTerritory.add(stm.ServiceTerritoryId);
            }
            
            serTerritorySet.addAll(serTerritory);
            assetFSEUpdate = [SELECT Id, Service_Territory__c, LocationId, location.visitoraddress.Longitude, location.visitoraddress.Latitude, 
                              Service_Territory__r.CountryCode, Primary_Field_Service_Engineer__c FROM Asset 
                              where (LocationId IN: updatedLocationSet)  
                              OR (Service_Territory__c IN: serTerritorySet) 
                              OR (location.visitoraddress.Latitude != null AND Service_Territory__c != NULL AND Id IN: setAssetId)
                              ORDER BY LastModifiedDate DESC];
            
            Set<Asset> assetUniqSet = new Set<Asset>(assetFSEUpdate); 
            assetFSEUpdate = new List<Asset>();
            assetFSEUpdate.addAll(assetUniqSet);
            
            return assetFSEUpdate;
        }
        catch(Exception ex){throw ex;}
    }
    
    /*Method to update the Primary Field Service Engineer and Service Territory fields of Asset*/
    global void execute(Database.BatchableContext bc, List<Asset> assetFSEUpdate){ 
        Set<Asset> assetset = new Set<Asset>();
        for(Asset asst:assetFSEUpdate){
            assetset.add(asst);
        }
        
        List<Asset> ass=new List<Asset>();        
        List<Asset> assetList=new List<Asset>();        
        Map<Id,Id> stiMap=new Map<Id,Id>();      
        try{       
            
            for(Asset asset1 : assetset) 
            {   
                Double longitude = asset1.location.visitoraddress.Longitude;
                Double latitude = asset1.location.visitoraddress.Latitude;
                Id serviceTerritoryId=null;
                if(longitude!=null && latitude!=null)
                {
                    serviceTerritoryId = FSL.PolygonUtils.getTerritoryIdByPolygons(double.valueOf(longitude),double.valueOf(latitude));
                    stiMap.put(asset1.id,serviceTerritoryId); //adding asset and service territory id in Map   
                }
                else
                {
                    stiMap.put(asset1.id,serviceTerritoryId);
                }       
            } 
            
            //Map<Id,Id> stmMap=new Map<Id,Id>();
            Map<Id,set<Id>> serviceTerritoryIdWithSTMIds = new Map<Id,Set<Id>>();           
            Set<Id> stiSet=new Set<Id>();
            stiSet.addAll(stiMap.values());
            
            for(ServiceTerritoryMember stm:[SELECT Id, ServiceResourceId, ServiceTerritoryId, ServiceResource.IsActive FROM ServiceTerritoryMember
                                                    where TerritoryType=:Constant.TerrType AND ServiceResource.IsActive = true
                                                    AND ServiceTerritoryId in:stiSet 
                                                    AND (EffectiveEndDate > YESTERDAY OR EffectiveEndDate = NULL)
                                                    order by EffectiveStartDate ASC])
            {
                //stmMap.put(stm.ServiceTerritoryId,stm.ServiceResourceId);
                if (serviceTerritoryIdWithSTMIds.containsKey(stm.ServiceTerritoryId)) {
                    serviceTerritoryIdWithSTMIds.get(stm.ServiceTerritoryId).add(stm.ServiceResourceId);
                } else {
                    serviceTerritoryIdWithSTMIds.put(stm.ServiceTerritoryId, new Set<Id>{stm.ServiceResourceId});
                }
            }
            for(Asset a: assetset){
                Id serviceTerritoryId = stiMap.get(a.id);
                if(serviceTerritoryId !=null && serviceTerritoryIdWithSTMIds.get(serviceTerritoryId) != null && serviceTerritoryIdWithSTMIds.get(serviceTerritoryId).size() == 1){      
                    a.Service_Territory__c = serviceTerritoryId;
                    a.Primary_Field_Service_Engineer__c= (serviceTerritoryIdWithSTMIds.get(serviceTerritoryId)).iterator().next();
                    assetList.add(a);
                }
            }
                        
            if(!assetList.isEmpty()) {
                Database.Update(assetList,false);
            }        }
        Catch(Exception ex){System.debug('Inside Catch'+ ex);
            throw ex;}
    }
    
    /*Finish method of the batch class*/
    global void finish(Database.BatchableContext bc){
        
    }
}