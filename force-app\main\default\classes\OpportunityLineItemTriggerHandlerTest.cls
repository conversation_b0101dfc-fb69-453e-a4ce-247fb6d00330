@isTest
public class OpportunityLineItemTriggerHandlerTest {
    @testSetup  
    static void testData() {
        
        Account acc = new Account();
        acc.Name = 'TestAccount#';
        acc.BillingCountry = 'United States';
        acc.BillingPostalCode = '48737';
        acc.BillingCity = 'KEYSTONE';
        acc.CurrencyIsoCode = 'USD';
        acc.ShippingState = 'Colorado';
        acc.ShippingCountry = 'United States';
        acc.ShippingPostalCode = '48737';
        acc.ShippingCity = 'KEYSTONE';
        insert acc;
        
        Pricebook2 newPriceBook = Datafactory.createPriceBook();
        
        Opportunity opp = new Opportunity();
        opp.RecordTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Capital - Standard').getRecordTypeId();
        opp.Name = 'TestOpportunity#';
        opp.AccountId = acc.Id;
        opp.Amount = 100;
        opp.CurrencyIsoCode = 'USD';
        opp.Type = 'LCM';
        opp.StageName = '0 - Targeting';
        opp.Business_Model__c = 'Sale';
        opp.CloseDate = date.parse(system.today().format());
        opp.Probability = 25;
        opp.Pricebook2Id = Test.getStandardPricebookId();
        Insert opp;  
        
        List<Product2> productList = new List<Product2>();
        
        Product2 objProduct = new Product2();
        objProduct.IsActive = TRUE;
        objProduct.Name = 'Test Product';
        objProduct.Unique_Product_Code__c = '50004';
        objProduct.ProductCode = '50004';
        productList.add(objProduct);
        
        Product2 objProduct1 = new Product2();
        objProduct1.IsActive = TRUE;
        objProduct1.Name = 'Test Product1';
        objProduct1.Unique_Product_Code__c = '********';
        objProduct1.ProductCode = '********';
        productList.add(objProduct1);
        
        Product2 objProduct2 = new Product2();
        objProduct2.IsActive = TRUE;
        objProduct2.Name = 'Test Product2';
        objProduct2.Unique_Product_Code__c = '102801';
        objProduct2.ProductCode = '102801';
        productList.add(objProduct2);
        
        Database.insert(productList);
        
        List<PricebookEntry> pricebookEntryList = new List<PricebookEntry>();
        List<PricebookEntry> stdPricebookEntryList = new List<PricebookEntry>();
        
        PricebookEntry standardPBE = new PricebookEntry(Pricebook2Id = Test.getStandardPricebookId(), Product2Id = objProduct.Id, UnitPrice = 1000, IsActive = true, CurrencyIsoCode = 'USD');
        stdPricebookEntryList.add(standardPBE);
        PricebookEntry standardPBE1 = new PricebookEntry(Pricebook2Id = Test.getStandardPricebookId(), Product2Id = objProduct1.Id, UnitPrice = 1000, IsActive = true, CurrencyIsoCode = 'USD');
        stdPricebookEntryList.add(standardPBE1);
        PricebookEntry standardPBE2 = new PricebookEntry(Pricebook2Id = Test.getStandardPricebookId(), Product2Id = objProduct2.Id, UnitPrice = 1000, IsActive = true, CurrencyIsoCode = 'USD');
        stdPricebookEntryList.add(standardPBE2);
        
        Database.Insert(stdPricebookEntryList);
    }
    
    @isTest
    static void testGenerateRelatedSKUs() {
        try{ 
            Test.startTest();
            
            Opportunity thisOpp = [SELECT Id, Pricebook2Id FROM Opportunity WHERE Name = 'TestOpportunity#'];
            thisOpp.Pricebook2Id = Test.getStandardPricebookId();
            update thisOpp;

            Product2 thisProduct = [SELECT Id FROM Product2 WHERE Unique_Product_Code__c = '50004'];

            PricebookEntry thisPriceBookEntry = [SELECT Id, Product2.ProductCode, Pricebook2Id FROM PricebookEntry WHERE Product2Id =: thisProduct.Id AND Pricebook2Id =: thisOpp.Pricebook2Id];
            
            OpportunityLineItem thisOppLineItem = new OpportunityLineItem();
            thisOppLineItem.OpportunityId = thisOpp.Id;
            thisOppLineItem.Product2Id = thisProduct.Id;
            thisOppLineItem.PricebookEntryId = thisPriceBookEntry.Id;
            thisOppLineItem.Quantity  = 1;
            
            insert thisOppLineItem;
            
            List<OpportunityLineItem> insertedOppLineItemList = [SELECT Id FROM OpportunityLineItem WHERE OpportunityId  =: thisOpp.Id];
            system.assertEquals(insertedOppLineItemList.size() > 1, true);
            Test.stopTest();
            
        }catch(Exception e){
            
            String result = e.getMessage();
            System.debug('Result: ' + result);
            throw e;
        }
    }
}