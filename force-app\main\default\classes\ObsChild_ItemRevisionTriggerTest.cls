@isTest
public with sharing class ObsChild_ItemRevisionTriggerTest {
    public static testMethod void ObsChild_AssemblyTriggerTest() {
        //Create Parent Item, Child Item, and BOM
        PDLM__Lifecycle__c lifecycleItem = new PDLM__Lifecycle__c(name='Parts', PDLM__primary_key__c = 'Parts');
        insert lifecycleItem;

        PDLM__Phase__c firstPhase = new PDLM__Phase__c(Name='First Phase', PDLM__order__c = 1, PDLM__Default_No_Change_Control__c = true, 
        PDLM__lifecycle__c = lifecycleItem.id, PDLM__Next_Phases__c = '["Discontinue Production","Pre-Production", "Production"]');
        insert firstPhase;

        PDLM__Phase__c secondPhase = new PDLM__Phase__c(Name='Discontinue Production', PDLM__order__c = 2, PDLM__Default_No_Change_Control__c = true, 
        PDLM__lifecycle__c = lifecycleItem.id, PDLM__Next_Phases__c = '["Pre-Production", "Production"]');
        insert secondPhase;

        PDLM__Autonumber__c autonumberItem = new PDLM__Autonumber__c(name='Autonumber for Part', PDLM__prefix__c = 'PART-', 
        PDLM__Length__c = 5, PDLM__Next_Number__c = 1);
        insert autonumberItem;

        PDLM__Category__c categoryForPartItem = new PDLM__Category__c(name='Part', PDLM__autonumber__c = autonumberItem.id, 
        PDLM__Type__c = 'Item', PDLM__Lifecycle__c = lifecycleItem.id);
        insert categoryForPartItem;

        //Create Paretnt Item and Item Revision
        PDLM__Item__c PartItem_Parent = new PDLM__Item__c(PDLM__Category__c = categoryForPartItem.id);
        insert PartItem_Parent;
        PDLM__Item_Revision__c PartItemRev_Parent = new PDLM__Item_Revision__c(PDLM__Master_Item__c = PartItem_Parent.id, 
                                                                               PDLM__Revision__c = 'A',
                                                                               PDLM__Released__c = True);
        insert PartItemRev_Parent;

        //Create Child Item and Item Revision
        PDLM__Item__c PartItem_Child = new PDLM__Item__c(PDLM__Category__c = categoryForPartItem.id);
        insert PartItem_Child;
        PDLM__Item_Revision__c PartItemRev_Child = new PDLM__Item_Revision__c(PDLM__Master_Item__c = PartItem_Child.id, 
                                                                              PDLM__Revision__c = 'A',
                                                                              PDLM__Released__c = True);
        insert PartItemRev_Child;

        //Create BOM for the released Parent and Child
        PDLM__Assembly__c ass = new PDLM__Assembly__c(PDLM__Item__c = PartItem_Child.id, PDLM__Item_Revision__c = PartItemRev_Parent.id);
        insert ass;

        //Create the second revision for Child and Parent, and put them into a BOM
        PDLM__Item_Revision__c PartItemRev_Parent2 = new PDLM__Item_Revision__c(PDLM__Master_Item__c = PartItem_Parent.id, 
                                                                               PDLM__Revision__c = 'B');
        insert PartItemRev_Parent2;

        PDLM__Item_Revision__c PartItemRev_Child2 = new PDLM__Item_Revision__c(PDLM__Master_Item__c = PartItem_Child.id, 
                                                                              PDLM__Revision__c = 'B',
                                                                              Revision_has_Parent__c = TRUE);
        insert PartItemRev_Child2;

        PDLM__Assembly__c ass2 = new PDLM__Assembly__c(PDLM__Item__c = PartItem_Child.id, PDLM__Item_Revision__c = PartItemRev_Parent2.id);
        insert ass2;

        //Create the second parent of the child - Added on 7/6/2020
        PDLM__Item__c PartItem_Parent_v2 = new PDLM__Item__c(PDLM__Category__c = categoryForPartItem.id);
        insert PartItem_Parent_v2;
        PDLM__Item_Revision__c PartItemRev_Parent_v2 = new PDLM__Item_Revision__c(PDLM__Master_Item__c = PartItem_Parent_v2.id, 
                                                                              PDLM__Revision__c = 'A',
                                                                              PDLM__Released__c = True);
        insert PartItemRev_Parent_v2;

        PDLM__Assembly__c ass_v2 = new PDLM__Assembly__c(PDLM__Item__c = PartItem_Child.id, PDLM__Item_Revision__c = PartItemRev_Parent_v2.id);
        insert ass_v2;

        test.startTest();
        //Add the second revision to add it to the BOM - Added on 7/6/2020
        PDLM__Item_Revision__c PartItemRev_Parent2_v2 = new PDLM__Item_Revision__c(PDLM__Master_Item__c = PartItem_Parent_v2.id, 
                                                                              PDLM__Revision__c = 'B');
        insert PartItemRev_Parent2_v2;

        PDLM__Assembly__c ass2_v2 = new PDLM__Assembly__c(PDLM__Item__c = PartItem_Child.id, PDLM__Item_Revision__c = PartItemRev_Parent2_v2.id);
        insert ass2_v2;

        PartItemRev_Child2.PDLM__Description__c = 'Test Desc to Fire PB to Set Revision_has_Parent__c to TRUE';
        update PartItemRev_Child2;
        
        delete ass2;
        delete ass2_v2;

        delete PartItemRev_Parent2_v2;

        test.stopTest();
    }
}