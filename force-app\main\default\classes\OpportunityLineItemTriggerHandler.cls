/*----------------------------------------------------------------------------------------
* Name: OpportunityLineItemTriggerHandler
* Description: Handler class for the trigger on the OpportunityLineItem Object
* 
*    Date     Version   Author    Summary of Changes
* ---------- --------- -------- ----------------------------------------------------------
* 04/19/2024    1.0      Alan    Created class for SFDC-4339
* 05/23/2024    2.0      Alan    Updated class for SFDC-4519 - Included Price Book Entry Matching Logic
* ----------------------------------------------------------------------------------------
*/
public without sharing class OpportunityLineItemTriggerHandler {
    
    public static void generateRelatedSKUs(List<OpportunityLineItem> newOppLineItemList){
        try{ 
            List<OpportunityLineItem> createOppLineItemList = new List<OpportunityLineItem>();
            Set<Id> oppLineItemIdSet = new Set<Id>();
            Set<Id> opportunityIdSet = new Set<Id>();
            Set<Id> pricebookIdSet = new Set<Id>();
            Set<String> currencyCodeSet = new Set<String>();
            
            Set<String> oppLineProductCodeSet = new Set<String>();
            for (OpportunityLineItem thisOppLineItem : newOppLineItemList) {
                
                oppLineProductCodeSet.add(thisOppLineItem.ProductCode);
                oppLineItemIdSet.add(thisOppLineItem.Id);
                opportunityIdSet.add(thisOppLineItem.OpportunityId);
            }
            
            Map<Id, String> oppLineItemUltParentMap = new Map<Id, String>();
            List<Opportunity> opportunityList = [SELECT Id, Account.Ultimate_Parent_Account__c, Pricebook2Id, CurrencyIsoCode FROM Opportunity WHERE Id IN: opportunityIdSet];
            
            for(Opportunity thisOpp : opportunityList){
                
                for(OpportunityLineItem currentOppLineItem : newOppLineItemList){
                    
                    if(currentOppLineItem.OpportunityId == thisOpp.Id && thisOpp.Account.Ultimate_Parent_Account__c != null){
                        
                        oppLineItemUltParentMap.put(currentOppLineItem.Id, thisOpp.Account.Ultimate_Parent_Account__c);
                    } else {
                        
                        oppLineItemUltParentMap.put(currentOppLineItem.Id, ' ');
                    }
                }
                
                if(thisOpp.Pricebook2Id != null){
                    
                    pricebookIdSet.add(thisOpp.Pricebook2Id);
                    currencyCodeSet.add(thisOpp.CurrencyIsoCode);
                }
            }

            Map<String, Pricing_Type_Metadata__mdt> pricingTypeMap = new Map<String, Pricing_Type_Metadata__mdt>();
            for (Pricing_Type_Metadata__mdt pmt : [SELECT Id, Parent_SKU__c, Auto_Add_SKUs__c, Category__c FROM Pricing_Type_Metadata__mdt WHERE Parent_SKU__c IN :oppLineProductCodeSet]) {
                
                pricingTypeMap.put(pmt.Parent_SKU__c, pmt);
            }
            
            if(!pricingTypeMap.isempty()){
                
                List<String> productSkuCollection = new List<String>();
                for (Pricing_Type_Metadata__mdt metadata : pricingTypeMap.values()) {
                    
                    if(metadata.Auto_Add_SKUs__c.contains(',')){
                        
                        productSkuCollection.addAll(metadata.Auto_Add_SKUs__c.split(','));
                        
                    }else{
                        
                        productSkuCollection.add(metadata.Auto_Add_SKUs__c);
                    }
                }
                
                List<PricebookEntry> pricebookEntryList = [SELECT Id, ProductCode, CurrencyIsoCode, Pricebook2Id FROM PricebookEntry WHERE ProductCode IN: productSkuCollection AND CurrencyIsoCode IN: currencyCodeSet AND Pricebook2Id IN: pricebookIdSet];
                
                Map<String, Map<String, PricebookEntry>> productCodeToPricebookEntryMap = new Map<String, Map<String, PricebookEntry>>();
                for (PricebookEntry entry : pricebookEntryList) {
                    if (!productCodeToPricebookEntryMap.containsKey(entry.ProductCode)) {
                        productCodeToPricebookEntryMap.put(entry.ProductCode, new Map<String, PricebookEntry>());
                    }
                    productCodeToPricebookEntryMap.get(entry.ProductCode).put(entry.CurrencyIsoCode, entry);
                }
                
                Map<String, Id> skuProductIdMap = new Map<String, Id>();
                for (Product2 product : [SELECT Id, Unique_Product_Code__c FROM Product2 WHERE Unique_Product_Code__c IN : productSkuCollection]) {
                    
                    skuProductIdMap.put(product.Unique_Product_Code__c, product.Id);
                }
                
                for (OpportunityLineItem oli : newOppLineItemList) {
                    
                    if (pricingTypeMap.containsKey(oli.ProductCode)) {
                        
                        String autoAddSKUs = pricingTypeMap.get(oli.ProductCode).Auto_Add_SKUs__c;
                        List<String> autoAddSkuCollection = new List<String>();
                        if(autoAddSKUs.contains(',')){
                            
                            autoAddSkuCollection = autoAddSKUs.split(',');
                        } 
                        else{
                            
                            autoAddSkuCollection.add(autoAddSKUs);
                        }
                        
                        for(String sku : autoAddSkuCollection){
                            
                            if (productCodeToPricebookEntryMap.containsKey(sku)) {
                                
                                Map<String, PricebookEntry> currencyPricebookEntryMap = productCodeToPricebookEntryMap.get(sku);
                                PricebookEntry relatedPricebookEntry = currencyPricebookEntryMap.get(oli.CurrencyIsoCode);
                                
                                if (relatedPricebookEntry != null) {
                                    
                                    if(skuProductIdMap.containsKey(sku) && skuProductIdMap.get(sku) != null){
                                        
                                        OpportunityLineItem newOli = new OpportunityLineItem();
                                        newOli.OpportunityId = oli.OpportunityId;
                                        newOli.Product2Id  = skuProductIdMap.get(sku);
                                        newOli.Quantity  = 1;
                                        newOli.PricebookEntryId = relatedPricebookEntry.Id;                                      
                                        
                                        String thisUltParentAcc = oppLineItemUltParentMap.get(oli.Id);
                                        if(sku.equals('113470-01')){
                                            
                                            if(!(thisUltParentAcc.equals('VA-GOVERNMENT') || thisUltParentAcc.equals('DOD-GOVERNMENT'))){
                                                
                                                createOppLineItemList.add(newOli);
                                            }
                                        } else {
                                            
                                            createOppLineItemList.add(newOli);
                                        }
                                    }
                                }  
                            }
                        }
                    }
                }
                
                if (!createOppLineItemList.isEmpty()) {
                    
                    insert createOppLineItemList;
                }
            }
        } catch (Exception e) {
            
            System.debug('Exception occurred on OpportunityLineItemTriggerHandler: ' + e.getMessage());
            throw e;
        }
    }
}