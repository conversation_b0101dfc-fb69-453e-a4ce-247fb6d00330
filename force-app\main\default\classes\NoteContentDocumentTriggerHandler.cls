/**
* <AUTHOR>
* @date        April 27, 2020
* @description Handler Class for note 
*/
public class NoteContentDocumentTriggerHandler {
    public static Map<Id,Datetime> oppIdDatetime = null;
    
    public static void getOppNoteDetails(List<ContentDocumentLink> noteList){
      
        oppIdDatetime = new Map<Id,Datetime>();
        for(ContentDocumentLink noteObj: noteList){
            oppIdDatetime.put(noteObj.LinkedEntityId, noteObj.systemmodstamp);
        }
            if(oppIdDatetime != null){
                List<Opportunity> oppList = [Select Id, IsClosed, last_modified_date_stamp__c from opportunity where Id =: oppIdDatetime.keyset()];
                updateOpp(oppList);
            }
    }
    public static void updateOpp(List<Opportunity> oppList){
    if(oppList != null && oppList.size() > 0){
        for(Opportunity opp :oppList){
            if(!opp.IsClosed){
                opp.last_modified_date_stamp__c = oppIdDatetime.get(opp.Id);
            }
            
        }
        update oppList;
    }
}
    
}