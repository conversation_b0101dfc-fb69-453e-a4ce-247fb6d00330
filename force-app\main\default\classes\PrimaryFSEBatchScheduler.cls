/**
* <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON> 
* @date :  24 Jan 2020, Last Modified: NA
* @description: FAUXCBUF-2406, Scheduler class to populate Primary FSE and Service Territory in Asset
*/
global class PrimaryFSEBatchScheduler implements Schedulable{

    /*Method to call the PrimaryBatchFSE class*/
    global void execute(SchedulableContext sc){
       PrimaryFSEBatch batch=new PrimaryFSEBatch();
       Database.executeBatch(batch,100);
    }
}